import { z } from 'zod';
import { IndianMobileSchema, EmailOTPSchema, VerifyOTPSchema, MobilePasswordLoginSchema } from '@/lib/schemas/authSchemas';

describe('authSchemas', () => {
  describe('IndianMobileSchema', () => {
    it('should validate a valid 10-digit Indian mobile number', () => {
      const result = IndianMobileSchema.safeParse('9876543210');
      expect(result.success).toBe(true);
    });

    it('should invalidate mobile numbers less than 10 digits', () => {
      const result = IndianMobileSchema.safeParse('12345');
      expect(result.success).toBe(false);
      expect(result.error?.issues[0].message).toBe('Mobile number must be 10 digits');
    });

    it('should invalidate mobile numbers more than 10 digits', () => {
      const result = IndianMobileSchema.safeParse('98765432101');
      expect(result.success).toBe(false);
      expect(result.error?.issues[0].message).toBe('Mobile number must be 10 digits');
    });

    it('should invalidate mobile numbers with invalid starting digit', () => {
      const result = IndianMobileSchema.safeParse('1234567890');
      expect(result.success).toBe(false);
      expect(result.error?.issues[0].message).toBe('Please enter a valid Indian mobile number');
    });

    it('should invalidate mobile numbers with non-digit characters', () => {
      const result = IndianMobileSchema.safeParse('98765abcde');
      expect(result.success).toBe(false);
      expect(result.error?.issues[0].message).toBe('Please enter a valid Indian mobile number');
    });

    it('should invalidate empty mobile number', () => {
      const result = IndianMobileSchema.safeParse('');
      expect(result.success).toBe(false);
      expect(result.error?.issues[0].message).toBe('Mobile number must be 10 digits');
    });
  });

  describe('EmailOTPSchema', () => {
    it('should validate a valid email address', () => {
      const result = EmailOTPSchema.safeParse({ email: '<EMAIL>' });
      expect(result.success).toBe(true);
    });

    it('should invalidate an invalid email format', () => {
      const result = EmailOTPSchema.safeParse({ email: 'invalid-email' });
      expect(result.success).toBe(false);
      expect(result.error?.issues[0].message).toBe('Please enter a valid email address');
    });

    it('should invalidate an empty email', () => {
      const result = EmailOTPSchema.safeParse({ email: '' });
      expect(result.success).toBe(false);
      expect(result.error?.issues[0].message).toBe('Email is required');
    });
  });

  describe('VerifyOTPSchema', () => {
    it('should validate a valid email and 6-digit OTP', () => {
      const result = VerifyOTPSchema.safeParse({ email: '<EMAIL>', token: '123456' });
      expect(result.success).toBe(true);
    });

    it('should invalidate an invalid email', () => {
      const result = VerifyOTPSchema.safeParse({ email: 'invalid-email', token: '123456' });
      expect(result.success).toBe(false);
      expect(result.error?.issues[0].message).toBe('Please enter a valid email address');
    });

    it('should invalidate OTP less than 6 digits', () => {
      const result = VerifyOTPSchema.safeParse({ email: '<EMAIL>', token: '123' });
      expect(result.success).toBe(false);
      expect(result.error?.issues[0].message).toBe('OTP must be 6 digits');
    });

    it('should invalidate OTP more than 6 digits', () => {
      const result = VerifyOTPSchema.safeParse({ email: '<EMAIL>', token: '1234567' });
      expect(result.success).toBe(false);
      expect(result.error?.issues[0].message).toBe('OTP must be 6 digits');
    });

    it('should invalidate OTP with non-digit characters', () => {
      const result = VerifyOTPSchema.safeParse({ email: '<EMAIL>', token: 'abcde1' });
      expect(result.success).toBe(false);
      expect(result.error?.issues[0].message).toBe('OTP must be 6 digits');
    });

    it('should invalidate empty OTP', () => {
      const result = VerifyOTPSchema.safeParse({ email: '<EMAIL>', token: '' });
      expect(result.success).toBe(false);
      expect(result.error?.issues[0].message).toBe('OTP must be 6 digits');
    });
  });

  describe('MobilePasswordLoginSchema', () => {
    it('should validate a valid mobile number and password', () => {
      const result = MobilePasswordLoginSchema.safeParse({ mobile: '9876543210', password: 'securepassword' });
      expect(result.success).toBe(true);
    });

    it('should invalidate with an invalid mobile number', () => {
      const result = MobilePasswordLoginSchema.safeParse({ mobile: '123', password: 'securepassword' });
      expect(result.success).toBe(false);
      expect(result.error?.issues[0].message).toBe('Mobile number must be 10 digits');
    });

    it('should invalidate with an empty password', () => {
      const result = MobilePasswordLoginSchema.safeParse({ mobile: '9876543210', password: '' });
      expect(result.success).toBe(false);
      expect(result.error?.issues[0].message).toBe('Password is required');
    });
  });
});
