import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { useRouter, useSearchParams } from 'next/navigation';
import { createClient } from '@/utils/supabase/client';
import AuthCallbackClient from '@/app/(main)/auth/callback/AuthCallbackClient';

// Mock Next.js navigation hooks
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
  useSearchParams: jest.fn(),
}));

// Mock Supabase client
jest.mock('@/utils/supabase/client', () => ({
  createClient: jest.fn(),
}));

// Mock redirectAfterLogin module
jest.mock('@/lib/actions/redirectAfterLogin', () => ({
  getPostLoginRedirectPath: jest.fn(),
}));

// Mock window.close and window.opener
const mockWindowClose = jest.fn();
let mockWindowOpenerLocationHref = 'initial';

Object.defineProperty(window, 'close', {
  writable: true,
  value: mockWindowClose,
});

Object.defineProperty(window, 'opener', {
  writable: true,
  value: {
    location: {
      get href() {
        return mockWindowOpenerLocationHref;
      },
      set href(value) {
        mockWindowOpenerLocationHref = value;
      },
    },
  },
});

describe('AuthCallbackClient', () => {
  const mockPush = jest.fn();
  const mockGetSearchParams = jest.fn();
  const mockGetUser = jest.fn();
  const mockGetPostLoginRedirectPath = require('@/lib/actions/redirectAfterLogin').getPostLoginRedirectPath;

  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockReturnValue({ push: mockPush });

    // Mock useSearchParams to return an object with get method and entries method
    const mockSearchParams = {
      get: mockGetSearchParams,
      entries: jest.fn(() => []), // Return empty array by default
    };
    (useSearchParams as jest.Mock).mockReturnValue(mockSearchParams);

    (createClient as jest.Mock).mockReturnValue({
      auth: {
        getUser: mockGetUser,
      },
    });

    // Reset window.opener.location.href mock
    mockWindowOpenerLocationHref = 'initial';
    Object.defineProperty(window.opener, 'location', {
      writable: true,
      value: {
        get href() {
          return mockWindowOpenerLocationHref;
        },
        set href(value) {
          mockWindowOpenerLocationHref = value;
        },
        origin: window.location.origin, // Simulate same origin
      },
    });

    // Mock localStorage
    Object.defineProperty(window, 'localStorage', {
      value: {
        setItem: jest.fn(),
        getItem: jest.fn(),
        removeItem: jest.fn(),
        clear: jest.fn(),
      },
      writable: true,
    });

    jest.useFakeTimers(); // Enable fake timers for setTimeout
  });

  afterEach(() => {
    jest.runOnlyPendingTimers();
    jest.useRealTimers();
  });

  it('shows loading state initially', () => {
    render(<AuthCallbackClient />);
    expect(screen.getByText('Authenticating...')).toBeInTheDocument();
    // Check for the SVG element with the animate-spin class
    const spinner = document.querySelector('.animate-spin');
    expect(spinner).toBeInTheDocument();
    expect(spinner).toHaveClass('animate-spin');
  });

  it('redirects to /login if auth fails and not a popup', async () => {
    mockGetSearchParams.mockReturnValue(jest.fn(() => null)); // Not a popup
    mockGetUser.mockResolvedValue({ data: { user: null }, error: new Error('Auth error') });

    render(<AuthCallbackClient />);

    await waitFor(() => {
      expect(mockPush).toHaveBeenCalledWith('/login');
      expect(mockWindowClose).not.toHaveBeenCalled();
    });
  });

  it('closes window if auth fails and is a popup', async () => {
    mockGetSearchParams.mockImplementation((key) => (key === 'closeWindow' ? 'true' : null));
    mockGetUser.mockResolvedValue({ data: { user: null }, error: new Error('Auth error') });

    render(<AuthCallbackClient />);

    await waitFor(() => {
      expect(mockWindowClose).toHaveBeenCalled();
      expect(mockPush).not.toHaveBeenCalled();
    });
  });

  it('redirects to default path for existing user with no redirect slug', async () => {
    mockGetSearchParams.mockReturnValue(jest.fn(() => null)); // No redirect or closeWindow
    mockGetUser.mockResolvedValue({ data: { user: { id: 'user123' } }, error: null });
    mockGetPostLoginRedirectPath.mockResolvedValue('/dashboard/customer');

    render(<AuthCallbackClient />);

    await waitFor(() => {
      expect(mockGetPostLoginRedirectPath).toHaveBeenCalledWith(expect.any(Object), 'user123');
      expect(mockPush).toHaveBeenCalledWith('/dashboard/customer');
      expect(mockWindowClose).not.toHaveBeenCalled();
    });
  });

  it('redirects to specified redirect slug for existing user', async () => {
    mockGetSearchParams.mockImplementation((key) => {
      if (key === 'redirect') return 'some-card-slug';
      return null;
    });
    mockGetUser.mockResolvedValue({ data: { user: { id: 'user123' } }, error: null });
    mockGetPostLoginRedirectPath.mockResolvedValue('/dashboard/customer'); // Should be ignored

    render(<AuthCallbackClient />);

    await waitFor(() => {
      expect(mockPush).toHaveBeenCalledWith('/some-card-slug');
    });
  });

  it('redirects to specified redirect slug with message for existing user', async () => {
    mockGetSearchParams.mockImplementation((key) => {
      if (key === 'redirect') return 'some-card-slug';
      if (key === 'message') return 'WelcomeBack';
      return null;
    });
    mockGetUser.mockResolvedValue({ data: { user: { id: 'user123' } }, error: null });

    render(<AuthCallbackClient />);

    await waitFor(() => {
      expect(mockPush).toHaveBeenCalledWith('/some-card-slug?message=WelcomeBack');
    });
  });

  it('handles new business user with redirect slug, stores in localStorage and redirects to onboarding', async () => {
    mockGetSearchParams.mockImplementation((key) => {
      if (key === 'redirect') return 'business-dashboard';
      if (key === 'message') return 'OnboardingRequired';
      return null;
    });
    mockGetUser.mockResolvedValue({ data: { user: { id: 'business_user123' } }, error: null });
    mockGetPostLoginRedirectPath.mockResolvedValue('/onboarding'); // Simulates new business user

    render(<AuthCallbackClient />);

    await waitFor(() => {
      expect(window.localStorage.setItem).toHaveBeenCalledWith('postOnboardingRedirect', 'business-dashboard');
      expect(window.localStorage.setItem).toHaveBeenCalledWith('postOnboardingMessage', 'OnboardingRequired');
      expect(mockPush).toHaveBeenCalledWith('/onboarding');
    });
  });

  it('handles new customer user with redirect slug, stores in localStorage and redirects to choose-role', async () => {
    mockGetSearchParams.mockImplementation((key) => {
      if (key === 'redirect') return 'customer-dashboard';
      if (key === 'message') return 'ChooseRole';
      return null;
    });
    mockGetUser.mockResolvedValue({ data: { user: { id: 'customer_user123' } }, error: null });
    mockGetPostLoginRedirectPath.mockResolvedValue('/choose-role'); // Simulates new customer user

    render(<AuthCallbackClient />);

    await waitFor(() => {
      expect(window.localStorage.setItem).toHaveBeenCalledWith('chooseRoleRedirect', 'customer-dashboard');
      expect(window.localStorage.setItem).toHaveBeenCalledWith('chooseRoleMessage', 'ChooseRole');
      expect(mockPush).toHaveBeenCalledWith('/choose-role?redirect=customer-dashboard&message=ChooseRole');
    });
  });

  it('handles next parameter for new business user', async () => {
    mockGetSearchParams.mockImplementation((key) => {
      if (key === 'next') return '/business-dashboard-next';
      return null;
    });
    mockGetUser.mockResolvedValue({ data: { user: { id: 'business_user123' } }, error: null });
    mockGetPostLoginRedirectPath.mockResolvedValue('/onboarding');

    render(<AuthCallbackClient />);

    await waitFor(() => {
      expect(window.localStorage.setItem).toHaveBeenCalledWith('postOnboardingRedirect', 'business-dashboard-next');
      expect(mockPush).toHaveBeenCalledWith('/onboarding');
    });
  });

  it('handles next parameter for new customer user', async () => {
    mockGetSearchParams.mockImplementation((key) => {
      if (key === 'next') return '/customer-dashboard-next';
      return null;
    });
    mockGetUser.mockResolvedValue({ data: { user: { id: 'customer_user123' } }, error: null });
    mockGetPostLoginRedirectPath.mockResolvedValue('/choose-role');

    render(<AuthCallbackClient />);

    await waitFor(() => {
      expect(window.localStorage.setItem).toHaveBeenCalledWith('chooseRoleRedirect', 'customer-dashboard-next');
      expect(mockPush).toHaveBeenCalledWith('/choose-role?redirect=customer-dashboard-next');
    });
  });

  it('redirects opener window and closes current window if is a popup and same origin', async () => {
    mockGetSearchParams.mockImplementation((key) => (key === 'closeWindow' ? 'true' : null));
    mockGetUser.mockResolvedValue({ data: { user: { id: 'user123' } }, error: null });
    mockGetPostLoginRedirectPath.mockResolvedValue('/dashboard/customer');

    render(<AuthCallbackClient />);

    await waitFor(() => {
      expect(mockWindowOpenerLocationHref).toBe('/dashboard/customer');
    });

    jest.advanceTimersByTime(500);

    await waitFor(() => {
      expect(mockWindowClose).toHaveBeenCalled();
    });
  });

  it('does not redirect opener if different origin', async () => {
    mockGetSearchParams.mockImplementation((key) => (key === 'closeWindow' ? 'true' : null));
    mockGetUser.mockResolvedValue({ data: { user: { id: 'user123' } }, error: null });
    mockGetPostLoginRedirectPath.mockResolvedValue('/dashboard/customer');

    // Simulate different origin
    const initialHref = mockWindowOpenerLocationHref;
    Object.defineProperty(window.opener, 'location', {
      writable: true,
      value: {
        get href() {
          return mockWindowOpenerLocationHref;
        },
        set href(value) {
          mockWindowOpenerLocationHref = value;
        },
        origin: 'http://different-origin.com',
      },
    });

    render(<AuthCallbackClient />);

    await waitFor(() => {
      // The href should not have changed from its initial value
      expect(mockWindowOpenerLocationHref).toBe(initialHref);
    });

    jest.advanceTimersByTime(500);

    await waitFor(() => {
      expect(mockWindowClose).toHaveBeenCalled();
    });
  });

  it('handles unexpected error during redirect path determination', async () => {
    mockGetSearchParams.mockReturnValue(jest.fn(() => null));
    mockGetUser.mockResolvedValue({ data: { user: { id: 'user123' } }, error: null });
    mockGetPostLoginRedirectPath.mockRejectedValue(new Error('Failed to get redirect path'));

    render(<AuthCallbackClient />);

    await waitFor(() => {
      expect(mockPush).toHaveBeenCalledWith('/?view=home');
    });
  });

  it('defaults to /?view=home if redirectPath is null/undefined', async () => {
    mockGetSearchParams.mockReturnValue(jest.fn(() => null));
    mockGetUser.mockResolvedValue({ data: { user: { id: 'user123' } }, error: null });
    mockGetPostLoginRedirectPath.mockResolvedValue(null); // Simulate null redirect path

    render(<AuthCallbackClient />);

    await waitFor(() => {
      expect(mockPush).toHaveBeenCalledWith('/?view=home');
    });
  });
});
